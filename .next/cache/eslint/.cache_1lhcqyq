[{"/Users/<USER>/byte-media/v1-go/src/app/analytics/page.tsx": "1", "/Users/<USER>/byte-media/v1-go/src/app/layout.tsx": "2", "/Users/<USER>/byte-media/v1-go/src/app/page.tsx": "3", "/Users/<USER>/byte-media/v1-go/src/app/reports/page.tsx": "4", "/Users/<USER>/byte-media/v1-go/src/app/search/page.tsx": "5", "/Users/<USER>/byte-media/v1-go/src/app/settings/page.tsx": "6", "/Users/<USER>/byte-media/v1-go/src/app/watchlist/page.tsx": "7", "/Users/<USER>/byte-media/v1-go/src/components/AdvancedFilters.tsx": "8", "/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx": "9", "/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx": "10", "/Users/<USER>/byte-media/v1-go/src/components/MapComponent.tsx": "11", "/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx": "12", "/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx": "13", "/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx": "14", "/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx": "15", "/Users/<USER>/byte-media/v1-go/src/components/StatsGrid.tsx": "16", "/Users/<USER>/byte-media/v1-go/src/lib/api.ts": "17", "/Users/<USER>/byte-media/v1-go/src/lib/realEstateAPI.ts": "18", "/Users/<USER>/byte-media/v1-go/src/lib/utils.ts": "19", "/Users/<USER>/byte-media/v1-go/src/types/property.ts": "20"}, {"size": 2576, "mtime": 1749320078471, "results": "21", "hashOfConfig": "22"}, {"size": 1900, "mtime": 1749319888168, "results": "23", "hashOfConfig": "22"}, {"size": 3189, "mtime": 1749319931844, "results": "24", "hashOfConfig": "22"}, {"size": 3199, "mtime": 1749320090094, "results": "25", "hashOfConfig": "22"}, {"size": 4897, "mtime": 1749320040960, "results": "26", "hashOfConfig": "22"}, {"size": 4600, "mtime": 1749320105864, "results": "27", "hashOfConfig": "22"}, {"size": 1283, "mtime": 1749320067276, "results": "28", "hashOfConfig": "22"}, {"size": 5512, "mtime": 1749320058392, "results": "29", "hashOfConfig": "22"}, {"size": 1090, "mtime": 1749319940933, "results": "30", "hashOfConfig": "22"}, {"size": 1816, "mtime": 1749319995192, "results": "31", "hashOfConfig": "22"}, {"size": 5238, "mtime": 1749320014302, "results": "32", "hashOfConfig": "22"}, {"size": 2937, "mtime": 1749319908620, "results": "33", "hashOfConfig": "22"}, {"size": 2477, "mtime": 1749320025196, "results": "34", "hashOfConfig": "22"}, {"size": 2213, "mtime": 1749319896980, "results": "35", "hashOfConfig": "22"}, {"size": 5673, "mtime": 1749319986451, "results": "36", "hashOfConfig": "22"}, {"size": 1225, "mtime": 1749319969444, "results": "37", "hashOfConfig": "22"}, {"size": 2426, "mtime": 1749319839533, "results": "38", "hashOfConfig": "22"}, {"size": 2519, "mtime": 1749319851550, "results": "39", "hashOfConfig": "22"}, {"size": 3546, "mtime": 1749319865305, "results": "40", "hashOfConfig": "22"}, {"size": 1382, "mtime": 1749319829303, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "k1e1pg", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/byte-media/v1-go/src/app/analytics/page.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/app/layout.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/app/page.tsx", ["102"], [], "/Users/<USER>/byte-media/v1-go/src/app/reports/page.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/app/search/page.tsx", ["103"], [], "/Users/<USER>/byte-media/v1-go/src/app/settings/page.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/app/watchlist/page.tsx", ["104"], [], "/Users/<USER>/byte-media/v1-go/src/components/AdvancedFilters.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/MapComponent.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/components/StatsGrid.tsx", [], [], "/Users/<USER>/byte-media/v1-go/src/lib/api.ts", [], [], "/Users/<USER>/byte-media/v1-go/src/lib/realEstateAPI.ts", [], [], "/Users/<USER>/byte-media/v1-go/src/lib/utils.ts", [], [], "/Users/<USER>/byte-media/v1-go/src/types/property.ts", [], [], {"ruleId": "105", "severity": 1, "message": "106", "line": 24, "column": 6, "nodeType": "107", "endLine": 24, "endColumn": 8, "suggestions": "108"}, {"ruleId": "105", "severity": 1, "message": "109", "line": 23, "column": 6, "nodeType": "107", "endLine": 23, "endColumn": 8, "suggestions": "110"}, {"ruleId": "111", "severity": 2, "message": "112", "line": 35, "column": 38, "nodeType": "113", "messageId": "114", "suggestions": "115"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["116"], "React Hook useEffect has a missing dependency: 'loadInitialProperties'. Either include it or remove the dependency array.", ["117"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["118", "119", "120", "121"], {"desc": "122", "fix": "123"}, {"desc": "124", "fix": "125"}, {"messageId": "126", "data": "127", "fix": "128", "desc": "129"}, {"messageId": "126", "data": "130", "fix": "131", "desc": "132"}, {"messageId": "126", "data": "133", "fix": "134", "desc": "135"}, {"messageId": "126", "data": "136", "fix": "137", "desc": "138"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "139", "text": "140"}, "Update the dependencies array to be: [loadInitialProperties]", {"range": "141", "text": "142"}, "replaceWithAlt", {"alt": "143"}, {"range": "144", "text": "145"}, "Replace with `&apos;`.", {"alt": "146"}, {"range": "147", "text": "148"}, "Replace with `&lsquo;`.", {"alt": "149"}, {"range": "150", "text": "151"}, "Replace with `&#39;`.", {"alt": "152"}, {"range": "153", "text": "154"}, "Replace with `&rsquo;`.", [1021, 1023], "[loadDashboardData]", [966, 968], "[loadInitialProperties]", "&apos;", [1074, 1160], "\n          Start adding properties you&apos;re interested in to keep track of them\n        ", "&lsquo;", [1074, 1160], "\n          Start adding properties you&lsquo;re interested in to keep track of them\n        ", "&#39;", [1074, 1160], "\n          Start adding properties you&#39;re interested in to keep track of them\n        ", "&rsquo;", [1074, 1160], "\n          Start adding properties you&rsquo;re interested in to keep track of them\n        "]