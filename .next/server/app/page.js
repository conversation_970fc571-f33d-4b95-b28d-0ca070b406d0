/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/byte-media/v1-go/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/byte-media/v1-go/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fcomponents%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fcomponents%2FProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fcomponents%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fcomponents%2FProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navbar.tsx */ \"(ssr)/./src/components/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(ssr)/./src/components/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5Y2ViYXllbnMlMkZieXRlLW1lZGlhJTJGdjEtZ28lMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZicnljZWJheWVucyUyRmJ5dGUtbWVkaWElMkZ2MS1nbyUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5Y2ViYXllbnMlMkZieXRlLW1lZGlhJTJGdjEtZ28lMkZzcmMlMkZjb21wb25lbnRzJTJGTmF2YmFyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMk5hdmJhciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmJyeWNlYmF5ZW5zJTJGYnl0ZS1tZWRpYSUyRnYxLWdvJTJGc3JjJTJGY29tcG9uZW50cyUyRlByb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUE4SDtBQUM5SDtBQUNBLHdLQUFvSSIsInNvdXJjZXMiOlsid2VicGFjazovL3ZhY2FudC1sYW5kLXNlYXJjaC1uZXh0anMvP2UxOWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJOYXZiYXJcIl0gKi8gXCIvVXNlcnMvYnJ5Y2ViYXllbnMvYnl0ZS1tZWRpYS92MS1nby9zcmMvY29tcG9uZW50cy9OYXZiYXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcm92aWRlcnNcIl0gKi8gXCIvVXNlcnMvYnJ5Y2ViYXllbnMvYnl0ZS1tZWRpYS92MS1nby9zcmMvY29tcG9uZW50cy9Qcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fcomponents%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fcomponents%2FProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYnJ5Y2ViYXllbnMlMkZieXRlLW1lZGlhJTJGdjEtZ28lMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXlGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmFjYW50LWxhbmQtc2VhcmNoLW5leHRqcy8/ODFjNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9icnljZWJheWVucy9ieXRlLW1lZGlhL3YxLWdvL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardHeader */ \"(ssr)/./src/components/DashboardHeader.tsx\");\n/* harmony import */ var _components_StatsGrid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/StatsGrid */ \"(ssr)/./src/components/StatsGrid.tsx\");\n/* harmony import */ var _components_SearchSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SearchSection */ \"(ssr)/./src/components/SearchSection.tsx\");\n/* harmony import */ var _components_MapAndResults__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/MapAndResults */ \"(ssr)/./src/components/MapAndResults.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Providers */ \"(ssr)/./src/components/Providers.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { showNotification } = (0,_components_Providers__WEBPACK_IMPORTED_MODULE_7__.useNotification)();\n    const loadDashboardData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setIsLoading(true);\n            const [statsData, searchResults] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_6__.propertyAPI.getDashboardStats(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_6__.propertyAPI.searchProperties({\n                    query: \"\",\n                    filters: {}\n                })\n            ]);\n            setStats(statsData);\n            setProperties(searchResults.results);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            showNotification(\"Failed to load dashboard data\", \"error\");\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        showNotification\n    ]);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, [\n        loadDashboardData\n    ]);\n    const handleSearch = async (query, searchFilters)=>{\n        try {\n            setIsSearching(true);\n            setSearchQuery(query);\n            setFilters(searchFilters);\n            const results = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.propertyAPI.searchProperties({\n                query,\n                filters: searchFilters\n            });\n            setProperties(results.results);\n        } catch (error) {\n            console.error(\"Error searching properties:\", error);\n            showNotification(\"Search failed. Please try again.\", \"error\");\n        } finally{\n            setIsSearching(false);\n        }\n    };\n    const handleSyncData = async ()=>{\n        try {\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.propertyAPI.syncProperties();\n            showNotification(`Successfully synced ${result.syncedCount} properties!`, \"success\");\n            // Reload dashboard data after sync\n            await loadDashboardData();\n        } catch (error) {\n            console.error(\"Error syncing data:\", error);\n            showNotification(\"Failed to sync data. Please try again.\", \"error\");\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"loading-spinner\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/byte-media/v1-go/src/app/page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/app/page.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/byte-media/v1-go/src/app/page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardHeader__WEBPACK_IMPORTED_MODULE_2__.DashboardHeader, {}, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/app/page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StatsGrid__WEBPACK_IMPORTED_MODULE_3__.StatsGrid, {\n                stats: stats\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/app/page.tsx\",\n                lineNumber: 91,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SearchSection__WEBPACK_IMPORTED_MODULE_4__.SearchSection, {\n                onSearch: handleSearch,\n                onSyncData: handleSyncData,\n                isSearching: isSearching\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/app/page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MapAndResults__WEBPACK_IMPORTED_MODULE_5__.MapAndResults, {\n                properties: properties,\n                searchQuery: searchQuery,\n                filters: filters\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/app/page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/byte-media/v1-go/src/app/page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DashboardHeader.tsx":
/*!********************************************!*\
  !*** ./src/components/DashboardHeader.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardHeader: () => (/* binding */ DashboardHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DashboardHeader auto */ \n\n\nfunction DashboardHeader() {\n    const [currentDate, setCurrentDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentDate(new Date());\n        }, 1000);\n        return ()=>clearInterval(timer);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 lg:mb-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-zillow-dark-gray mb-2\",\n                            children: \"Hello, Admin!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Welcome to your vacant land search dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-right text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium\",\n                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatDate)(currentDate)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTime)(currentDate)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/DashboardHeader.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DashboardHeader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MapAndResults.tsx":
/*!******************************************!*\
  !*** ./src/components/MapAndResults.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MapAndResults: () => (/* binding */ MapAndResults)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MapComponent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MapComponent */ \"(ssr)/./src/components/MapComponent.tsx\");\n/* harmony import */ var _PropertyList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PropertyList */ \"(ssr)/./src/components/PropertyList.tsx\");\n/* __next_internal_client_entry_do_not_use__ MapAndResults auto */ \n\n\n\nfunction MapAndResults({ properties, searchQuery, filters }) {\n    const [selectedProperty, setSelectedProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handlePropertySelect = (property)=>{\n        setSelectedProperty(property);\n    };\n    const handleMapPropertyClick = (propertyId)=>{\n        const property = properties.find((p)=>p.id === propertyId);\n        if (property) {\n            setSelectedProperty(property);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 h-[600px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2 card p-0 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MapComponent__WEBPACK_IMPORTED_MODULE_2__.MapComponent, {\n                    properties: properties,\n                    selectedProperty: selectedProperty,\n                    onPropertyClick: handleMapPropertyClick\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card overflow-hidden flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b border-zillow-border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"Search Results\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    properties.length,\n                                    \" properties\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PropertyList__WEBPACK_IMPORTED_MODULE_3__.PropertyList, {\n                            properties: properties,\n                            selectedProperty: selectedProperty,\n                            onPropertySelect: handlePropertySelect\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapAndResults.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MapAndResults.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MapComponent.tsx":
/*!*****************************************!*\
  !*** ./src/components/MapComponent.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MapComponent: () => (/* binding */ MapComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var mapbox_gl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mapbox-gl */ \"(ssr)/./node_modules/mapbox-gl/dist/mapbox-gl.js\");\n/* harmony import */ var mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mapbox_gl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MapComponent auto */ \n\n\n\n\n// Set Mapbox access token\n(mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().accessToken) = \"pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg\" || 0;\nfunction MapComponent({ properties, selectedProperty, onPropertyClick }) {\n    const mapContainer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const map = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const markers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [isMapLoaded, setIsMapLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize map\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mapContainer.current || map.current) return;\n        map.current = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Map)({\n            container: mapContainer.current,\n            style: \"mapbox://styles/mapbox/light-v11\",\n            center: [\n                _lib_utils__WEBPACK_IMPORTED_MODULE_3__.DAYTONA_BEACH_CENTER.lng,\n                _lib_utils__WEBPACK_IMPORTED_MODULE_3__.DAYTONA_BEACH_CENTER.lat\n            ],\n            zoom: 12\n        });\n        map.current.on(\"load\", ()=>{\n            setIsMapLoaded(true);\n        });\n        // Add navigation controls\n        map.current.addControl(new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().NavigationControl)(), \"top-right\");\n        return ()=>{\n            if (map.current) {\n                map.current.remove();\n                map.current = null;\n            }\n        };\n    }, []);\n    // Update markers when properties change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!map.current || !isMapLoaded) return;\n        // Clear existing markers\n        markers.current.forEach((marker)=>marker.remove());\n        markers.current = [];\n        // Add new markers\n        properties.forEach((property)=>{\n            if (!property.latitude || !property.longitude) return;\n            // Create custom marker element\n            const markerElement = document.createElement(\"div\");\n            markerElement.className = \"custom-marker\";\n            markerElement.innerHTML = `\n        <div class=\"bg-zillow-blue text-white px-2 py-1 rounded-lg shadow-lg text-sm font-medium cursor-pointer hover:bg-zillow-blue-dark transition-colors\">\n          ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatPrice)(property.price)}\n        </div>\n      `;\n            // Create popup\n            const popup = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Popup)({\n                offset: 25,\n                closeButton: true,\n                closeOnClick: false\n            }).setHTML(`\n        <div class=\"p-4 max-w-sm\">\n          <h4 class=\"font-bold text-zillow-blue mb-2\">${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatPrice)(property.price)}</h4>\n          <p class=\"font-medium mb-2\">${property.address}</p>\n          <p class=\"text-sm text-gray-600 mb-3\">${property.description}</p>\n          <div class=\"grid grid-cols-2 gap-2 text-xs text-gray-600\">\n            <div><strong>Size:</strong> ${property.size}</div>\n            <div><strong>Zoning:</strong> ${property.zoning}</div>\n            <div><strong>Chain Potential:</strong> ${property.chainLeasePotential}</div>\n            <div><strong>Proximity:</strong> ${property.proximity}</div>\n          </div>\n        </div>\n      `);\n            // Create marker\n            const marker = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Marker)(markerElement).setLngLat([\n                property.longitude,\n                property.latitude\n            ]).setPopup(popup).addTo(map.current);\n            // Add click handler\n            markerElement.addEventListener(\"click\", ()=>{\n                onPropertyClick(property.id);\n            });\n            markers.current.push(marker);\n        });\n        // Fit map to show all markers\n        if (properties.length > 0) {\n            const bounds = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().LngLatBounds)();\n            properties.forEach((property)=>{\n                if (property.latitude && property.longitude) {\n                    bounds.extend([\n                        property.longitude,\n                        property.latitude\n                    ]);\n                }\n            });\n            map.current.fitBounds(bounds, {\n                padding: 50,\n                maxZoom: 15\n            });\n        }\n    }, [\n        properties,\n        isMapLoaded,\n        onPropertyClick\n    ]);\n    // Handle selected property\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!map.current || !selectedProperty || !selectedProperty.latitude || !selectedProperty.longitude) return;\n        // Center map on selected property\n        map.current.flyTo({\n            center: [\n                selectedProperty.longitude,\n                selectedProperty.latitude\n            ],\n            zoom: 16,\n            duration: 1000\n        });\n        // Open popup for selected property\n        const selectedMarker = markers.current.find((marker)=>{\n            const lngLat = marker.getLngLat();\n            return Math.abs(lngLat.lat - selectedProperty.latitude) < 0.0001 && Math.abs(lngLat.lng - selectedProperty.longitude) < 0.0001;\n        });\n        if (selectedMarker) {\n            selectedMarker.togglePopup();\n        }\n    }, [\n        selectedProperty\n    ]);\n    if (false) {}\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: mapContainer,\n                className: \"w-full h-full\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapComponent.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            !isMapLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"loading-spinner\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapComponent.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapComponent.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/MapComponent.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MapComponent.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Heart,Menu,Search,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Heart,Menu,Search,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Heart,Menu,Search,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Heart,Menu,Search,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Heart,Menu,Search,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Heart,Menu,Search,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Heart,Menu,Search,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Heart,Menu,Search,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \n\n\n\n\nconst navItems = [\n    {\n        href: \"/\",\n        label: \"Dashboard\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        href: \"/search\",\n        label: \"Land Search\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        href: \"/watchlist\",\n        label: \"Watch List\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        href: \"/analytics\",\n        label: \"Market Analytics\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        href: \"/reports\",\n        label: \"Reports\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        href: \"/settings\",\n        label: \"Settings\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nfunction Navbar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                className: \"lg:hidden fixed top-4 left-4 z-50 p-2 bg-zillow-blue text-white rounded-lg shadow-lg\",\n                children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 29\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_FileText_Heart_Menu_Search_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 47\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40\",\n                onClick: ()=>setIsMobileMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: `\n          fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-zillow-blue to-zillow-blue-dark\n          text-white z-40 shadow-zillow-lg transition-transform duration-300 ease-in-out\n          ${isMobileMenuOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\"}\n        `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 border-b border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold mb-2\",\n                                children: \"Vacant Land Search\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm opacity-80\",\n                                children: \"Daytona Beach, FL\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-4\",\n                        children: navItems.map((item)=>{\n                            const Icon = item.icon;\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: item.href,\n                                className: `navbar-item ${isActive ? \"active\" : \"\"}`,\n                                onClick: ()=>setIsMobileMenuOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        size: 20,\n                                        className: \"mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.label\n                                ]\n                            }, item.href, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 right-0 p-6 border-t border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs opacity-60\",\n                                children: \"\\xa9 2024 Vacant Land Search\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs opacity-60 mt-1\",\n                                children: \"Powered by Real Estate API\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9OYXZiYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0M7QUFDWDtBQUNpQjtBQVV4QjtBQUV0QixNQUFNWSxXQUFXO0lBQ2Y7UUFDRUMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLE1BQU1YLHNJQUFTQTtJQUNqQjtJQUNBO1FBQ0VTLE1BQU07UUFDTkMsT0FBTztRQUNQQyxNQUFNVixzSUFBTUE7SUFDZDtJQUNBO1FBQ0VRLE1BQU07UUFDTkMsT0FBTztRQUNQQyxNQUFNVCxzSUFBS0E7SUFDYjtJQUNBO1FBQ0VPLE1BQU07UUFDTkMsT0FBTztRQUNQQyxNQUFNUixzSUFBVUE7SUFDbEI7SUFDQTtRQUNFTSxNQUFNO1FBQ05DLE9BQU87UUFDUEMsTUFBTVAsc0lBQVFBO0lBQ2hCO0lBQ0E7UUFDRUssTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLE1BQU1OLHNJQUFRQTtJQUNoQjtDQUNEO0FBRU0sU0FBU087SUFDZCxNQUFNQyxXQUFXZCw0REFBV0E7SUFDNUIsTUFBTSxDQUFDZSxrQkFBa0JDLG9CQUFvQixHQUFHbEIsK0NBQVFBLENBQUM7SUFFekQscUJBQ0U7OzBCQUVFLDhEQUFDbUI7Z0JBQ0NDLFNBQVMsSUFBTUYsb0JBQW9CLENBQUNEO2dCQUNwQ0ksV0FBVTswQkFFVEosaUNBQW1CLDhEQUFDUCx1SUFBQ0E7b0JBQUNZLE1BQU07Ozs7O3lDQUFTLDhEQUFDYix1SUFBSUE7b0JBQUNhLE1BQU07Ozs7Ozs7Ozs7O1lBSW5ETCxrQ0FDQyw4REFBQ007Z0JBQ0NGLFdBQVU7Z0JBQ1ZELFNBQVMsSUFBTUYsb0JBQW9COzs7Ozs7MEJBS3ZDLDhEQUFDTTtnQkFDQ0gsV0FBVyxDQUFDOzs7VUFHVixFQUFFSixtQkFBbUIsa0JBQWtCLHFDQUFxQztRQUM5RSxDQUFDOztrQ0FHRCw4REFBQ007d0JBQUlGLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBSUYsV0FBVTswQ0FBMEI7Ozs7OzswQ0FDekMsOERBQUNFO2dDQUFJRixXQUFVOzBDQUFxQjs7Ozs7Ozs7Ozs7O2tDQUl0Qyw4REFBQ0U7d0JBQUlGLFdBQVU7a0NBQ1pWLFNBQVNjLEdBQUcsQ0FBQyxDQUFDQzs0QkFDYixNQUFNQyxPQUFPRCxLQUFLWixJQUFJOzRCQUN0QixNQUFNYyxXQUFXWixhQUFhVSxLQUFLZCxJQUFJOzRCQUV2QyxxQkFDRSw4REFBQ1gsaURBQUlBO2dDQUVIVyxNQUFNYyxLQUFLZCxJQUFJO2dDQUNmUyxXQUFXLENBQUMsWUFBWSxFQUFFTyxXQUFXLFdBQVcsR0FBRyxDQUFDO2dDQUNwRFIsU0FBUyxJQUFNRixvQkFBb0I7O2tEQUVuQyw4REFBQ1M7d0NBQUtMLE1BQU07d0NBQUlELFdBQVU7Ozs7OztvQ0FDekJLLEtBQUtiLEtBQUs7OytCQU5OYSxLQUFLZCxJQUFJOzs7Ozt3QkFTcEI7Ozs7OztrQ0FJRiw4REFBQ1c7d0JBQUlGLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBSUYsV0FBVTswQ0FBcUI7Ozs7OzswQ0FHcEMsOERBQUNFO2dDQUFJRixXQUFVOzBDQUEwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPbkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92YWNhbnQtbGFuZC1zZWFyY2gtbmV4dGpzLy4vc3JjL2NvbXBvbmVudHMvTmF2YmFyLnRzeD85YTZkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7XG4gIEJhckNoYXJ0MyxcbiAgU2VhcmNoLFxuICBIZWFydCxcbiAgVHJlbmRpbmdVcCxcbiAgRmlsZVRleHQsXG4gIFNldHRpbmdzLFxuICBNZW51LFxuICBYLFxufSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5jb25zdCBuYXZJdGVtcyA9IFtcbiAge1xuICAgIGhyZWY6ICcvJyxcbiAgICBsYWJlbDogJ0Rhc2hib2FyZCcsXG4gICAgaWNvbjogQmFyQ2hhcnQzLFxuICB9LFxuICB7XG4gICAgaHJlZjogJy9zZWFyY2gnLFxuICAgIGxhYmVsOiAnTGFuZCBTZWFyY2gnLFxuICAgIGljb246IFNlYXJjaCxcbiAgfSxcbiAge1xuICAgIGhyZWY6ICcvd2F0Y2hsaXN0JyxcbiAgICBsYWJlbDogJ1dhdGNoIExpc3QnLFxuICAgIGljb246IEhlYXJ0LFxuICB9LFxuICB7XG4gICAgaHJlZjogJy9hbmFseXRpY3MnLFxuICAgIGxhYmVsOiAnTWFya2V0IEFuYWx5dGljcycsXG4gICAgaWNvbjogVHJlbmRpbmdVcCxcbiAgfSxcbiAge1xuICAgIGhyZWY6ICcvcmVwb3J0cycsXG4gICAgbGFiZWw6ICdSZXBvcnRzJyxcbiAgICBpY29uOiBGaWxlVGV4dCxcbiAgfSxcbiAge1xuICAgIGhyZWY6ICcvc2V0dGluZ3MnLFxuICAgIGxhYmVsOiAnU2V0dGluZ3MnLFxuICAgIGljb246IFNldHRpbmdzLFxuICB9LFxuXTtcblxuZXhwb3J0IGZ1bmN0aW9uIE5hdmJhcigpIHtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuICBjb25zdCBbaXNNb2JpbGVNZW51T3Blbiwgc2V0SXNNb2JpbGVNZW51T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgey8qIE1vYmlsZSBtZW51IGJ1dHRvbiAqL31cbiAgICAgIDxidXR0b25cbiAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNb2JpbGVNZW51T3BlbighaXNNb2JpbGVNZW51T3Blbil9XG4gICAgICAgIGNsYXNzTmFtZT1cImxnOmhpZGRlbiBmaXhlZCB0b3AtNCBsZWZ0LTQgei01MCBwLTIgYmctemlsbG93LWJsdWUgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZ1wiXG4gICAgICA+XG4gICAgICAgIHtpc01vYmlsZU1lbnVPcGVuID8gPFggc2l6ZT17MjR9IC8+IDogPE1lbnUgc2l6ZT17MjR9IC8+fVxuICAgICAgPC9idXR0b24+XG5cbiAgICAgIHsvKiBPdmVybGF5IGZvciBtb2JpbGUgKi99XG4gICAgICB7aXNNb2JpbGVNZW51T3BlbiAmJiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9XCJsZzpoaWRkZW4gZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIHotNDBcIlxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlTWVudU9wZW4oZmFsc2UpfVxuICAgICAgICAvPlxuICAgICAgKX1cblxuICAgICAgey8qIE5hdmJhciAqL31cbiAgICAgIDxuYXZcbiAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgZml4ZWQgbGVmdC0wIHRvcC0wIGgtZnVsbCB3LTgwIGJnLWdyYWRpZW50LXRvLWIgZnJvbS16aWxsb3ctYmx1ZSB0by16aWxsb3ctYmx1ZS1kYXJrXG4gICAgICAgICAgdGV4dC13aGl0ZSB6LTQwIHNoYWRvdy16aWxsb3ctbGcgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0XG4gICAgICAgICAgJHtpc01vYmlsZU1lbnVPcGVuID8gJ3RyYW5zbGF0ZS14LTAnIDogJy10cmFuc2xhdGUteC1mdWxsIGxnOnRyYW5zbGF0ZS14LTAnfVxuICAgICAgICBgfVxuICAgICAgPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtOCBib3JkZXItYiBib3JkZXItd2hpdGUvMTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBtYi0yXCI+VmFjYW50IExhbmQgU2VhcmNoPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIG9wYWNpdHktODBcIj5EYXl0b25hIEJlYWNoLCBGTDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTmF2aWdhdGlvbiBJdGVtcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS00XCI+XG4gICAgICAgICAge25hdkl0ZW1zLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgSWNvbiA9IGl0ZW0uaWNvbjtcbiAgICAgICAgICAgIGNvbnN0IGlzQWN0aXZlID0gcGF0aG5hbWUgPT09IGl0ZW0uaHJlZjtcblxuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICBrZXk9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgbmF2YmFyLWl0ZW0gJHtpc0FjdGl2ZSA/ICdhY3RpdmUnIDogJyd9YH1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01vYmlsZU1lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxJY29uIHNpemU9ezIwfSBjbGFzc05hbWU9XCJtci0zXCIgLz5cbiAgICAgICAgICAgICAgICB7aXRlbS5sYWJlbH1cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9KX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZvb3RlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMCBsZWZ0LTAgcmlnaHQtMCBwLTYgYm9yZGVyLXQgYm9yZGVyLXdoaXRlLzEwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIG9wYWNpdHktNjBcIj5cbiAgICAgICAgICAgIMKpIDIwMjQgVmFjYW50IExhbmQgU2VhcmNoXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIG9wYWNpdHktNjAgbXQtMVwiPlxuICAgICAgICAgICAgUG93ZXJlZCBieSBSZWFsIEVzdGF0ZSBBUElcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L25hdj5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiTGluayIsInVzZVBhdGhuYW1lIiwiQmFyQ2hhcnQzIiwiU2VhcmNoIiwiSGVhcnQiLCJUcmVuZGluZ1VwIiwiRmlsZVRleHQiLCJTZXR0aW5ncyIsIk1lbnUiLCJYIiwibmF2SXRlbXMiLCJocmVmIiwibGFiZWwiLCJpY29uIiwiTmF2YmFyIiwicGF0aG5hbWUiLCJpc01vYmlsZU1lbnVPcGVuIiwic2V0SXNNb2JpbGVNZW51T3BlbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJjbGFzc05hbWUiLCJzaXplIiwiZGl2IiwibmF2IiwibWFwIiwiaXRlbSIsIkljb24iLCJpc0FjdGl2ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PropertyList.tsx":
/*!*****************************************!*\
  !*** ./src/components/PropertyList.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyList: () => (/* binding */ PropertyList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ PropertyList auto */ \n\n\nfunction PropertyList({ properties, selectedProperty, onPropertySelect }) {\n    if (properties.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 text-center text-gray-500\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No properties found matching your criteria.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm mt-2\",\n                    children: \"Try adjusting your search filters.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-4\",\n        children: properties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: ()=>onPropertySelect(property),\n                className: `property-card ${selectedProperty?.id === property.id ? \"border-zillow-blue bg-zillow-blue-light\" : \"\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xl font-bold text-zillow-blue mb-2\",\n                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(property.price)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium mb-3\",\n                        children: property.address\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-sm text-gray-600 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Size:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    property.size\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"$/sq ft:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" $\",\n                                    property.pricePerSqFt\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Habitability:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    property.habitability\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Days on Market:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    property.daysOnMarket\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Proximity:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    property.proximity\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-2 py-1 rounded text-xs font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getZoningColor)(property.zoning)}`,\n                                children: property.zoning\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-2 py-1 rounded text-xs font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getChainPotentialColor)(property.chainLeasePotential)}`,\n                                children: [\n                                    \"Chain: \",\n                                    property.chainLeasePotential\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, property.id, true, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/PropertyList.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PropertyList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers),\n/* harmony export */   useNotification: () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useNotification,Providers auto */ \n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useNotification() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (!context) {\n        throw new Error(\"useNotification must be used within a NotificationProvider\");\n    }\n    return context;\n}\nfunction Providers({ children }) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, type = \"info\")=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const notification = {\n            id,\n            message,\n            type\n        };\n        setNotifications((prev)=>[\n                ...prev,\n                notification\n            ]);\n        // Auto remove after 5 seconds\n        setTimeout(()=>{\n            setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n        }, 5000);\n    }, []);\n    const removeNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: {\n            showNotification\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-5 right-5 z-50 space-y-2\",\n                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `notification notification-${notification.type} animate-slide-in`,\n                        onClick: ()=>removeNotification(notification.id),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>removeNotification(notification.id),\n                                    className: \"ml-4 text-white hover:text-gray-200\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this)\n                    }, notification.id, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SearchSection.tsx":
/*!******************************************!*\
  !*** ./src/components/SearchSection.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchSection: () => (/* binding */ SearchSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ SearchSection auto */ \n\n\nfunction SearchSection({ onSearch, onSyncData, isSearching }) {\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSyncing, setIsSyncing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSync, setLastSync] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Never\");\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        onSearch(query, filters);\n    };\n    const handleSyncClick = async ()=>{\n        setIsSyncing(true);\n        try {\n            await onSyncData();\n            setLastSync(new Date().toLocaleTimeString());\n        } finally{\n            setIsSyncing(false);\n        }\n    };\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value || undefined\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-zillow-dark-gray mb-4 lg:mb-0\",\n                        children: \"Search Vacant Land in Daytona Beach\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSyncClick,\n                                disabled: isSyncing,\n                                className: `btn-success flex items-center space-x-2 ${isSyncing ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        size: 16,\n                                        className: isSyncing ? \"animate-spin\" : \"\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isSyncing ? \"Syncing...\" : \"Sync Live Data\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"Last sync: \",\n                                    lastSync\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: query,\n                                    onChange: (e)=>setQuery(e.target.value),\n                                    placeholder: \"Search by address, area, or keywords...\",\n                                    className: \"input-field\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSearching,\n                                className: `btn-primary flex items-center space-x-2 ${isSearching ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isSearching ? \"Searching...\" : \"Search\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-zillow-dark-gray mb-2\",\n                                        children: \"Zoning Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: filters.zoning || \"\",\n                                        onChange: (e)=>handleFilterChange(\"zoning\", e.target.value),\n                                        className: \"select-field\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"All Zoning\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Commercial\",\n                                                children: \"Commercial\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Residential\",\n                                                children: \"Residential\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Industrial\",\n                                                children: \"Industrial\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Mixed Use\",\n                                                children: \"Mixed Use\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-zillow-dark-gray mb-2\",\n                                        children: \"Min Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: filters.minPrice || \"\",\n                                        onChange: (e)=>handleFilterChange(\"minPrice\", parseInt(e.target.value)),\n                                        className: \"select-field\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"No Min\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"50000\",\n                                                children: \"$50,000\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"100000\",\n                                                children: \"$100,000\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"200000\",\n                                                children: \"$200,000\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"500000\",\n                                                children: \"$500,000\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-zillow-dark-gray mb-2\",\n                                        children: \"Max Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: filters.maxPrice || \"\",\n                                        onChange: (e)=>handleFilterChange(\"maxPrice\", parseInt(e.target.value)),\n                                        className: \"select-field\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"No Max\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"100000\",\n                                                children: \"$100,000\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"200000\",\n                                                children: \"$200,000\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"500000\",\n                                                children: \"$500,000\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"1000000\",\n                                                children: \"$1,000,000\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-zillow-dark-gray mb-2\",\n                                        children: \"Chain Potential\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: filters.chainPotential || \"\",\n                                        onChange: (e)=>handleFilterChange(\"chainPotential\", e.target.value),\n                                        className: \"select-field\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"All Potential\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Very High\",\n                                                children: \"Very High\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"High\",\n                                                children: \"High\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Medium\",\n                                                children: \"Medium\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Low\",\n                                                children: \"Low\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/SearchSection.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SearchSection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StatsGrid.tsx":
/*!**************************************!*\
  !*** ./src/components/StatsGrid.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsGrid: () => (/* binding */ StatsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ StatsGrid auto */ \n\n\nfunction StatsGrid({ stats }) {\n    const statItems = [\n        {\n            label: \"Total Properties\",\n            value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(stats.totalProperties),\n            color: \"text-zillow-blue\"\n        },\n        {\n            label: \"Average Price\",\n            value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(stats.averagePrice),\n            color: \"text-zillow-blue\"\n        },\n        {\n            label: \"New Listings (24h)\",\n            value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(stats.newListings),\n            color: \"text-zillow-blue\"\n        },\n        {\n            label: \"Sold This Month\",\n            value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(stats.soldThisMonth),\n            color: \"text-zillow-blue\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: statItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"stat-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-3xl font-bold mb-2 ${item.color}`,\n                        children: item.value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/StatsGrid.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 text-sm\",\n                        children: item.label\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/StatsGrid.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/components/StatsGrid.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/byte-media/v1-go/src/components/StatsGrid.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StatsGrid.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIError: () => (/* binding */ APIError),\n/* harmony export */   propertyAPI: () => (/* binding */ propertyAPI)\n/* harmony export */ });\nconst API_BASE_URL = \"https://gold-braid-458901-v2.uc.r.appspot.com\" || 0;\nclass APIError extends Error {\n    constructor(message, status){\n        super(message);\n        this.status = status;\n        this.name = \"APIError\";\n    }\n}\nasync function fetchAPI(endpoint, options) {\n    const url = `${API_BASE_URL}${endpoint}`;\n    try {\n        const response = await fetch(url, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options?.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            throw new APIError(`API request failed: ${response.statusText}`, response.status);\n        }\n        return await response.json();\n    } catch (error) {\n        if (error instanceof APIError) {\n            throw error;\n        }\n        throw new APIError(`Network error: ${error instanceof Error ? error.message : \"Unknown error\"}`, 0);\n    }\n}\nconst propertyAPI = {\n    // Search properties with filters\n    searchProperties: async (searchRequest)=>{\n        return fetchAPI(\"/api/search\", {\n            method: \"POST\",\n            body: JSON.stringify(searchRequest)\n        });\n    },\n    // Get dashboard statistics\n    getDashboardStats: async ()=>{\n        return fetchAPI(\"/api/dashboard/stats\");\n    },\n    // Sync live property data\n    syncProperties: async ()=>{\n        return fetchAPI(\"/api/sync-properties\", {\n            method: \"POST\"\n        });\n    },\n    // Refresh all data\n    refreshData: async ()=>{\n        return fetchAPI(\"/api/refresh-data\", {\n            method: \"POST\"\n        });\n    },\n    // Get property details by ID\n    getPropertyDetails: async (id)=>{\n        return fetchAPI(`/property?id=${id}`);\n    },\n    // Get property details by address\n    getPropertyByAddress: async (address)=>{\n        return fetchAPI(`/property?address=${encodeURIComponent(address)}`);\n    },\n    // Check API status\n    getStatus: async ()=>{\n        return fetchAPI(\"/status\");\n    }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ATLANTIC_OCEAN_COORDS: () => (/* binding */ ATLANTIC_OCEAN_COORDS),\n/* harmony export */   DAYTONA_BEACH_CENTER: () => (/* binding */ DAYTONA_BEACH_CENTER),\n/* harmony export */   calculateDistance: () => (/* binding */ calculateDistance),\n/* harmony export */   calculateProximityToBeach: () => (/* binding */ calculateProximityToBeach),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   getChainPotentialColor: () => (/* binding */ getChainPotentialColor),\n/* harmony export */   getZoningColor: () => (/* binding */ getZoningColor)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n\nfunction cn(...inputs) {\n    return (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs);\n}\nfunction formatPrice(price) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency: \"USD\",\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(price);\n}\nfunction formatNumber(num) {\n    return new Intl.NumberFormat(\"en-US\").format(num);\n}\nfunction formatDate(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return new Intl.DateTimeFormat(\"en-US\", {\n        weekday: \"long\",\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    }).format(dateObj);\n}\nfunction formatTime(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return new Intl.DateTimeFormat(\"en-US\", {\n        hour: \"numeric\",\n        minute: \"2-digit\",\n        second: \"2-digit\"\n    }).format(dateObj);\n}\nfunction getZoningColor(zoning) {\n    const zoningLower = zoning.toLowerCase();\n    if (zoningLower.includes(\"commercial\")) {\n        return \"bg-zillow-blue-light text-zillow-blue\";\n    } else if (zoningLower.includes(\"residential\")) {\n        return \"bg-yellow-100 text-yellow-700\";\n    } else if (zoningLower.includes(\"industrial\")) {\n        return \"bg-purple-100 text-purple-700\";\n    } else if (zoningLower.includes(\"mixed\")) {\n        return \"bg-green-100 text-green-700\";\n    }\n    return \"bg-gray-100 text-gray-700\";\n}\nfunction getChainPotentialColor(potential) {\n    const potentialLower = potential.toLowerCase();\n    if (potentialLower.includes(\"very high\")) {\n        return \"bg-green-100 text-green-800\";\n    } else if (potentialLower.includes(\"high\")) {\n        return \"bg-green-50 text-green-700\";\n    } else if (potentialLower.includes(\"medium\")) {\n        return \"bg-yellow-100 text-yellow-700\";\n    } else if (potentialLower.includes(\"low\")) {\n        return \"bg-red-100 text-red-700\";\n    }\n    return \"bg-gray-100 text-gray-700\";\n}\nfunction calculateDistance(lat1, lng1, lat2, lng2) {\n    const R = 3959; // Earth's radius in miles\n    const dLat = (lat2 - lat1) * (Math.PI / 180);\n    const dLng = (lng2 - lng1) * (Math.PI / 180);\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) * Math.sin(dLng / 2) * Math.sin(dLng / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n}\n// Daytona Beach coordinates for proximity calculations\nconst DAYTONA_BEACH_CENTER = {\n    lat: 29.2108,\n    lng: -81.0228\n};\nconst ATLANTIC_OCEAN_COORDS = {\n    lat: 29.2108,\n    lng: -80.9773\n};\nfunction calculateProximityToBeach(lat, lng) {\n    const distance = calculateDistance(lat, lng, ATLANTIC_OCEAN_COORDS.lat, ATLANTIC_OCEAN_COORDS.lng);\n    if (distance < 0.5) {\n        return \"Beachfront\";\n    } else if (distance < 1) {\n        return `${distance.toFixed(1)} miles to beach`;\n    } else if (distance < 5) {\n        return `${distance.toFixed(1)} miles to beach`;\n    } else {\n        return `${distance.toFixed(0)} miles to beach`;\n    }\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"916290647557\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmFjYW50LWxhbmQtc2VhcmNoLW5leHRqcy8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OGUzZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjkxNjI5MDY0NzU1N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(rsc)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./src/components/Providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Vacant Land Search - Daytona Beach FL | Admin Dashboard\",\n    description: \"Professional real estate admin panel for vacant land search in Daytona Beach, Florida. Find commercial and residential zoning opportunities with chain lease potential.\",\n    keywords: \"vacant land, Daytona Beach, real estate, commercial zoning, residential zoning, chain lease, property search\",\n    authors: [\n        {\n            name: \"Vacant Land Search Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"Vacant Land Search - Daytona Beach FL\",\n        description: \"Professional real estate admin panel for vacant land opportunities\",\n        type: \"website\",\n        locale: \"en_US\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: `https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.js`,\n                        async: true\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_3__.Providers, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-zillow-gray\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__.Navbar, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"ml-0 lg:ml-80 min-h-screen\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/app/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navbar: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx#Navbar`);


/***/ }),

/***/ "(rsc)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e1),
/* harmony export */   useNotification: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#useNotification`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/mapbox-gl"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbrycebayens%2Fbyte-media%2Fv1-go&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();