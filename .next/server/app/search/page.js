(()=>{var e={};e.id=797,e.ids=[797],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},363:(e,s,l)=>{"use strict";l.r(s),l.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>c}),l(4856),l(8567),l(5866);var r=l(3191),t=l(8716),i=l(7922),a=l.n(i),n=l(5231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);l.d(s,o);let c=["",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,4856)),"/Users/<USER>/byte-media/v1-go/src/app/search/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(l.bind(l,8567)),"/Users/<USER>/byte-media/v1-go/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(l.t.bind(l,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/byte-media/v1-go/src/app/search/page.tsx"],x="/search/page",u={require:l,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2537:(e,s,l)=>{Promise.resolve().then(l.bind(l,4045))},1137:(e,s,l)=>{"use strict";l.d(s,{Z:()=>r});let r=(0,l(6557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},4045:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>u});var r=l(326),t=l(7577),i=l(4582),a=l(27),n=l(1137),o=l(4019);function c({onApply:e,onClose:s}){let[l,i]=(0,t.useState)({}),a=(e,s)=>{i(l=>({...l,[e]:s||void 0}))};return(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(n.Z,{size:20,className:"text-zillow-blue"}),r.jsx("h3",{className:"text-lg font-semibold text-zillow-dark-gray",children:"Advanced Filters"})]}),r.jsx("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:r.jsx(o.Z,{size:20})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Zoning Type"}),(0,r.jsxs)("select",{value:l.zoning||"",onChange:e=>a("zoning",e.target.value),className:"select-field",children:[r.jsx("option",{value:"",children:"All Zoning Types"}),r.jsx("option",{value:"Commercial",children:"Commercial"}),r.jsx("option",{value:"Residential",children:"Residential"}),r.jsx("option",{value:"Industrial",children:"Industrial"}),r.jsx("option",{value:"Mixed Use",children:"Mixed Use"}),r.jsx("option",{value:"Agricultural",children:"Agricultural"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Minimum Price"}),(0,r.jsxs)("select",{value:l.minPrice||"",onChange:e=>a("minPrice",parseInt(e.target.value)),className:"select-field",children:[r.jsx("option",{value:"",children:"No Minimum"}),r.jsx("option",{value:"25000",children:"$25,000"}),r.jsx("option",{value:"50000",children:"$50,000"}),r.jsx("option",{value:"75000",children:"$75,000"}),r.jsx("option",{value:"100000",children:"$100,000"}),r.jsx("option",{value:"150000",children:"$150,000"}),r.jsx("option",{value:"200000",children:"$200,000"}),r.jsx("option",{value:"300000",children:"$300,000"}),r.jsx("option",{value:"500000",children:"$500,000"}),r.jsx("option",{value:"750000",children:"$750,000"}),r.jsx("option",{value:"1000000",children:"$1,000,000"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Maximum Price"}),(0,r.jsxs)("select",{value:l.maxPrice||"",onChange:e=>a("maxPrice",parseInt(e.target.value)),className:"select-field",children:[r.jsx("option",{value:"",children:"No Maximum"}),r.jsx("option",{value:"50000",children:"$50,000"}),r.jsx("option",{value:"75000",children:"$75,000"}),r.jsx("option",{value:"100000",children:"$100,000"}),r.jsx("option",{value:"150000",children:"$150,000"}),r.jsx("option",{value:"200000",children:"$200,000"}),r.jsx("option",{value:"300000",children:"$300,000"}),r.jsx("option",{value:"500000",children:"$500,000"}),r.jsx("option",{value:"750000",children:"$750,000"}),r.jsx("option",{value:"1000000",children:"$1,000,000"}),r.jsx("option",{value:"2000000",children:"$2,000,000"}),r.jsx("option",{value:"5000000",children:"$5,000,000"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Chain Lease Potential"}),(0,r.jsxs)("select",{value:l.chainPotential||"",onChange:e=>a("chainPotential",e.target.value),className:"select-field",children:[r.jsx("option",{value:"",children:"All Potential Levels"}),r.jsx("option",{value:"Very High",children:"Very High"}),r.jsx("option",{value:"High",children:"High"}),r.jsx("option",{value:"Medium",children:"Medium"}),r.jsx("option",{value:"Low",children:"Low"}),r.jsx("option",{value:"None",children:"None"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-zillow-border",children:[r.jsx("button",{onClick:()=>{i({})},className:"btn-secondary",children:"Reset Filters"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),r.jsx("button",{onClick:()=>{e(l)},className:"btn-primary",children:"Apply Filters"})]})]})]})}var d=l(8069),x=l(5596);function u(){let[e,s]=(0,t.useState)([]),[l,n]=(0,t.useState)(!0),[o,u]=(0,t.useState)(!1),[h,p]=(0,t.useState)(""),[m,j]=(0,t.useState)({}),[v,g]=(0,t.useState)(!1),{showNotification:y}=(0,x.l)(),b=async()=>{try{n(!0);let e=await d.G.searchProperties({query:"",filters:{}});s(e.results)}catch(e){console.error("Error loading properties:",e),y("Failed to load properties","error")}finally{n(!1)}},f=async(e,l)=>{try{u(!0),p(e),j(l);let r=await d.G.searchProperties({query:e,filters:l});s(r.results),y(`Found ${r.total} properties`,"success")}catch(e){console.error("Error searching properties:",e),y("Search failed. Please try again.","error")}finally{u(!1)}},N=async()=>{try{let e=await d.G.syncProperties();y(`Successfully synced ${e.syncedCount} properties!`,"success"),await b()}catch(e){console.error("Error syncing data:",e),y("Failed to sync data. Please try again.","error")}};return l?r.jsx("div",{className:"p-8",children:r.jsx("div",{className:"flex items-center justify-center h-64",children:r.jsx("div",{className:"loading-spinner"})})}):(0,r.jsxs)("div",{className:"p-8 space-y-8",children:[r.jsx("div",{className:"card",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-zillow-dark-gray mb-2",children:"Vacant Land Search"}),r.jsx("p",{className:"text-gray-600",children:"Find the perfect vacant land opportunities in Daytona Beach, FL"})]}),r.jsx("div",{className:"mt-4 lg:mt-0",children:(0,r.jsxs)("button",{onClick:()=>g(!v),className:"btn-secondary",children:[v?"Hide":"Show"," Advanced Filters"]})})]})}),r.jsx(i.y,{onSearch:f,onSyncData:N,isSearching:o}),v&&r.jsx(c,{onApply:e=>{f(h,{...m,...e}),g(!1)},onClose:()=>g(!1)}),r.jsx("div",{className:"card",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold text-zillow-dark-gray",children:"Search Results"}),(0,r.jsxs)("p",{className:"text-gray-600",children:[e.length," properties found",h&&` for "${h}"`]})]}),e.length>0&&r.jsx("div",{className:"text-sm text-gray-500",children:"Showing all results"})]})}),r.jsx(a.a,{properties:e,searchQuery:h,filters:m})]})}},4856:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>r});let r=(0,l(8570).createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/app/search/page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var l=e=>s(s.s=e),r=s.X(0,[415,398,147],()=>l(363));module.exports=r})();