exports.id=147,exports.ids=[147],exports.modules={9412:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},4099:(e,s,t)=>{Promise.resolve().then(t.bind(t,8015)),Promise.resolve().then(t.bind(t,5596))},27:(e,s,t)=>{"use strict";t.d(s,{a:()=>d});var i=t(326),a=t(7577),r=t(810),l=t.n(r),n=t(1223);function c({properties:e,selectedProperty:s,onPropertyClick:t}){let r=(0,a.useRef)(null);(0,a.useRef)(null),(0,a.useRef)([]);let[l,n]=(0,a.useState)(!1);return(0,i.jsxs)("div",{className:"w-full h-full relative",children:[i.jsx("div",{ref:r,className:"w-full h-full"}),!l&&i.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100",children:i.jsx("div",{className:"loading-spinner"})})]})}function o({properties:e,selectedProperty:s,onPropertySelect:t}){return 0===e.length?(0,i.jsxs)("div",{className:"p-6 text-center text-gray-500",children:[i.jsx("p",{children:"No properties found matching your criteria."}),i.jsx("p",{className:"text-sm mt-2",children:"Try adjusting your search filters."})]}):i.jsx("div",{className:"p-6 space-y-4",children:e.map(e=>(0,i.jsxs)("div",{onClick:()=>t(e),className:`property-card ${s?.id===e.id?"border-zillow-blue bg-zillow-blue-light":""}`,children:[i.jsx("div",{className:"text-xl font-bold text-zillow-blue mb-2",children:(0,n.T4)(e.price)}),i.jsx("div",{className:"font-medium mb-3",children:e.address}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm text-gray-600 mb-3",children:[(0,i.jsxs)("div",{children:[i.jsx("strong",{children:"Size:"})," ",e.size]}),(0,i.jsxs)("div",{children:[i.jsx("strong",{children:"$/sq ft:"})," $",e.pricePerSqFt]}),(0,i.jsxs)("div",{children:[i.jsx("strong",{children:"Habitability:"})," ",e.habitability]}),(0,i.jsxs)("div",{children:[i.jsx("strong",{children:"Days on Market:"})," ",e.daysOnMarket]}),(0,i.jsxs)("div",{className:"col-span-2",children:[i.jsx("strong",{children:"Proximity:"})," ",e.proximity]})]}),(0,i.jsxs)("div",{className:"flex flex-wrap gap-2",children:[i.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${(0,n.qp)(e.zoning)}`,children:e.zoning}),(0,i.jsxs)("span",{className:`px-2 py-1 rounded text-xs font-medium ${(0,n.Wc)(e.chainLeasePotential)}`,children:["Chain: ",e.chainLeasePotential]})]})]},e.id))})}function d({properties:e,searchQuery:s,filters:t}){let[r,l]=(0,a.useState)(null);return(0,i.jsxs)("section",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 h-[600px]",children:[i.jsx("div",{className:"lg:col-span-2 card p-0 overflow-hidden",children:i.jsx(c,{properties:e,selectedProperty:r,onPropertyClick:s=>{let t=e.find(e=>e.id===s);t&&l(t)}})}),(0,i.jsxs)("div",{className:"card overflow-hidden flex flex-col",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-zillow-border",children:[i.jsx("h3",{className:"text-lg font-semibold",children:"Search Results"}),(0,i.jsxs)("span",{className:"text-sm text-gray-600",children:[e.length," properties"]})]}),i.jsx("div",{className:"flex-1 overflow-y-auto",children:i.jsx(o,{properties:e,selectedProperty:r,onPropertySelect:e=>{l(e)}})})]})]})}l().accessToken="pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg"},8015:(e,s,t)=>{"use strict";t.d(s,{Navbar:()=>g});var i=t(326),a=t(7577),r=t(434),l=t(5047),n=t(6464),c=t(8307),o=t(7427),d=t(7069),h=t(6283),x=t(8378),m=t(4019),u=t(748);let p=[{href:"/",label:"Dashboard",icon:n.Z},{href:"/search",label:"Land Search",icon:c.Z},{href:"/watchlist",label:"Watch List",icon:o.Z},{href:"/analytics",label:"Market Analytics",icon:d.Z},{href:"/reports",label:"Reports",icon:h.Z},{href:"/settings",label:"Settings",icon:x.Z}];function g(){let e=(0,l.usePathname)(),[s,t]=(0,a.useState)(!1);return(0,i.jsxs)(i.Fragment,{children:[i.jsx("button",{onClick:()=>t(!s),className:"lg:hidden fixed top-4 left-4 z-50 p-2 bg-zillow-blue text-white rounded-lg shadow-lg",children:s?i.jsx(m.Z,{size:24}):i.jsx(u.Z,{size:24})}),s&&i.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>t(!1)}),(0,i.jsxs)("nav",{className:`
          fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-zillow-blue to-zillow-blue-dark
          text-white z-40 shadow-zillow-lg transition-transform duration-300 ease-in-out
          ${s?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        `,children:[(0,i.jsxs)("div",{className:"p-8 border-b border-white/10",children:[i.jsx("div",{className:"text-2xl font-bold mb-2",children:"Vacant Land Search"}),i.jsx("div",{className:"text-sm opacity-80",children:"Daytona Beach, FL"})]}),i.jsx("div",{className:"py-4",children:p.map(s=>{let a=s.icon,l=e===s.href;return(0,i.jsxs)(r.default,{href:s.href,className:`navbar-item ${l?"active":""}`,onClick:()=>t(!1),children:[i.jsx(a,{size:20,className:"mr-3"}),s.label]},s.href)})}),(0,i.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t border-white/10",children:[i.jsx("div",{className:"text-xs opacity-60",children:"\xa9 2024 Vacant Land Search"}),i.jsx("div",{className:"text-xs opacity-60 mt-1",children:"Powered by Real Estate API"})]})]})]})}},5596:(e,s,t)=>{"use strict";t.d(s,{Providers:()=>n,l:()=>l});var i=t(326),a=t(7577);let r=(0,a.createContext)(void 0);function l(){let e=(0,a.useContext)(r);if(!e)throw Error("useNotification must be used within a NotificationProvider");return e}function n({children:e}){let[s,t]=(0,a.useState)([]),l=(0,a.useCallback)((e,s="info")=>{let i=Math.random().toString(36).substr(2,9),a={id:i,message:e,type:s};t(e=>[...e,a]),setTimeout(()=>{t(e=>e.filter(e=>e.id!==i))},5e3)},[]),n=(0,a.useCallback)(e=>{t(s=>s.filter(s=>s.id!==e))},[]);return(0,i.jsxs)(r.Provider,{value:{showNotification:l},children:[e,i.jsx("div",{className:"fixed top-5 right-5 z-50 space-y-2",children:s.map(e=>i.jsx("div",{className:`notification notification-${e.type} animate-slide-in`,onClick:()=>n(e.id),children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[i.jsx("span",{children:e.message}),i.jsx("button",{onClick:()=>n(e.id),className:"ml-4 text-white hover:text-gray-200",children:"\xd7"})]})},e.id))})]})}},4582:(e,s,t)=>{"use strict";t.d(s,{y:()=>n});var i=t(326),a=t(7577),r=t(1405),l=t(8307);function n({onSearch:e,onSyncData:s,isSearching:t}){let[n,c]=(0,a.useState)(""),[o,d]=(0,a.useState)({}),[h,x]=(0,a.useState)(!1),[m,u]=(0,a.useState)("Never"),p=async()=>{x(!0);try{await s(),u(new Date().toLocaleTimeString())}finally{x(!1)}},g=(e,s)=>{d(t=>({...t,[e]:s||void 0}))};return(0,i.jsxs)("section",{className:"card",children:[(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6",children:[i.jsx("h2",{className:"text-xl font-semibold text-zillow-dark-gray mb-4 lg:mb-0",children:"Search Vacant Land in Daytona Beach"}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("button",{onClick:p,disabled:h,className:`btn-success flex items-center space-x-2 ${h?"opacity-50 cursor-not-allowed":""}`,children:[i.jsx(r.Z,{size:16,className:h?"animate-spin":""}),i.jsx("span",{children:h?"Syncing...":"Sync Live Data"})]}),(0,i.jsxs)("span",{className:"text-xs text-gray-500",children:["Last sync: ",m]})]})]}),(0,i.jsxs)("form",{onSubmit:s=>{s.preventDefault(),e(n,o)},className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[i.jsx("div",{className:"flex-1",children:i.jsx("input",{type:"text",value:n,onChange:e=>c(e.target.value),placeholder:"Search by address, area, or keywords...",className:"input-field"})}),(0,i.jsxs)("button",{type:"submit",disabled:t,className:`btn-primary flex items-center space-x-2 ${t?"opacity-50 cursor-not-allowed":""}`,children:[i.jsx(l.Z,{size:16}),i.jsx("span",{children:t?"Searching...":"Search"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Zoning Type"}),(0,i.jsxs)("select",{value:o.zoning||"",onChange:e=>g("zoning",e.target.value),className:"select-field",children:[i.jsx("option",{value:"",children:"All Zoning"}),i.jsx("option",{value:"Commercial",children:"Commercial"}),i.jsx("option",{value:"Residential",children:"Residential"}),i.jsx("option",{value:"Industrial",children:"Industrial"}),i.jsx("option",{value:"Mixed Use",children:"Mixed Use"})]})]}),(0,i.jsxs)("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Min Price"}),(0,i.jsxs)("select",{value:o.minPrice||"",onChange:e=>g("minPrice",parseInt(e.target.value)),className:"select-field",children:[i.jsx("option",{value:"",children:"No Min"}),i.jsx("option",{value:"50000",children:"$50,000"}),i.jsx("option",{value:"100000",children:"$100,000"}),i.jsx("option",{value:"200000",children:"$200,000"}),i.jsx("option",{value:"500000",children:"$500,000"})]})]}),(0,i.jsxs)("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Max Price"}),(0,i.jsxs)("select",{value:o.maxPrice||"",onChange:e=>g("maxPrice",parseInt(e.target.value)),className:"select-field",children:[i.jsx("option",{value:"",children:"No Max"}),i.jsx("option",{value:"100000",children:"$100,000"}),i.jsx("option",{value:"200000",children:"$200,000"}),i.jsx("option",{value:"500000",children:"$500,000"}),i.jsx("option",{value:"1000000",children:"$1,000,000"})]})]}),(0,i.jsxs)("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Chain Potential"}),(0,i.jsxs)("select",{value:o.chainPotential||"",onChange:e=>g("chainPotential",e.target.value),className:"select-field",children:[i.jsx("option",{value:"",children:"All Potential"}),i.jsx("option",{value:"Very High",children:"Very High"}),i.jsx("option",{value:"High",children:"High"}),i.jsx("option",{value:"Medium",children:"Medium"}),i.jsx("option",{value:"Low",children:"Low"})]})]})]})]})]})}},8069:(e,s,t)=>{"use strict";t.d(s,{G:()=>r});class i extends Error{constructor(e,s){super(e),this.status=s,this.name="APIError"}}async function a(e,s){let t=`https://gold-braid-458901-v2.uc.r.appspot.com${e}`;try{let e=await fetch(t,{headers:{"Content-Type":"application/json",...s?.headers},...s});if(!e.ok)throw new i(`API request failed: ${e.statusText}`,e.status);return await e.json()}catch(e){if(e instanceof i)throw e;throw new i(`Network error: ${e instanceof Error?e.message:"Unknown error"}`,0)}}let r={searchProperties:async e=>a("/api/search",{method:"POST",body:JSON.stringify(e)}),getDashboardStats:async()=>a("/api/dashboard/stats"),syncProperties:async()=>a("/api/sync-properties",{method:"POST"}),refreshData:async()=>a("/api/refresh-data",{method:"POST"}),getPropertyDetails:async e=>a(`/property?id=${e}`),getPropertyByAddress:async e=>a(`/property?address=${encodeURIComponent(e)}`),getStatus:async()=>a("/status")}},1223:(e,s,t)=>{"use strict";function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}function a(e){return new Intl.NumberFormat("en-US").format(e)}function r(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}).format(s)}function l(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{hour:"numeric",minute:"2-digit",second:"2-digit"}).format(s)}function n(e){let s=e.toLowerCase();return s.includes("commercial")?"bg-zillow-blue-light text-zillow-blue":s.includes("residential")?"bg-yellow-100 text-yellow-700":s.includes("industrial")?"bg-purple-100 text-purple-700":s.includes("mixed")?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700"}function c(e){let s=e.toLowerCase();return s.includes("very high")?"bg-green-100 text-green-800":s.includes("high")?"bg-green-50 text-green-700":s.includes("medium")?"bg-yellow-100 text-yellow-700":s.includes("low")?"bg-red-100 text-red-700":"bg-gray-100 text-gray-700"}t.d(s,{T4:()=>i,Wc:()=>c,mr:()=>l,p6:()=>r,qp:()=>n,uf:()=>a})},8567:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>o});var i=t(9510),a=t(5317),r=t.n(a);t(5023);var l=t(8570);let n=(0,l.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Navbar.tsx#Navbar`);(0,l.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#useNotification`);let c=(0,l.createProxy)(String.raw`/Users/<USER>/byte-media/v1-go/src/components/Providers.tsx#Providers`),o={title:"Vacant Land Search - Daytona Beach FL | Admin Dashboard",description:"Professional real estate admin panel for vacant land search in Daytona Beach, Florida. Find commercial and residential zoning opportunities with chain lease potential.",keywords:"vacant land, Daytona Beach, real estate, commercial zoning, residential zoning, chain lease, property search",authors:[{name:"Vacant Land Search Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"Vacant Land Search - Daytona Beach FL",description:"Professional real estate admin panel for vacant land opportunities",type:"website",locale:"en_US"}};function d({children:e}){return(0,i.jsxs)("html",{lang:"en",children:[(0,i.jsxs)("head",{children:[i.jsx("link",{rel:"icon",href:"/favicon.ico"}),i.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),i.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),i.jsx("script",{src:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.js",async:!0}),i.jsx("link",{href:"https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.css",rel:"stylesheet"})]}),i.jsx("body",{className:r().className,children:i.jsx(c,{children:(0,i.jsxs)("div",{className:"min-h-screen bg-zillow-gray",children:[i.jsx(n,{}),i.jsx("main",{className:"ml-0 lg:ml-80 min-h-screen",children:e})]})})})]})}},5023:()=>{}};