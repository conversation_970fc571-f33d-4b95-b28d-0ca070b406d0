"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[474],{9763:function(e,t,n){n.d(t,{Z:function(){return l}});var i=n(2265),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,t)=>{let n=(0,i.forwardRef)((n,l)=>{let{color:a="currentColor",size:c=24,strokeWidth:o=2,absoluteStrokeWidth:d,className:u="",children:x,...h}=n;return(0,i.createElement)("svg",{ref:l,...r,width:c,height:c,stroke:a,strokeWidth:d?24*Number(o)/Number(c):o,className:["lucide","lucide-".concat(s(e)),u].join(" "),...h},[...t.map(e=>{let[t,n]=e;return(0,i.createElement)(t,n)}),...Array.isArray(x)?x:[x]])});return n.displayName="".concat(e),n}},3247:function(e,t,n){n.d(t,{Z:function(){return i}});let i=(0,n(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},1900:function(e,t,n){n.d(t,{a:function(){return d}});var i=n(7437),r=n(2265),s=n(2520),l=n.n(s),a=n(3448);function c(e){let{properties:t,selectedProperty:n,onPropertyClick:s}=e,c=(0,r.useRef)(null),o=(0,r.useRef)(null),d=(0,r.useRef)([]),[u,x]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{if(c.current&&!o.current)return o.current=new(l()).Map({container:c.current,style:"mapbox://styles/mapbox/light-v11",center:[a.ZK.lng,a.ZK.lat],zoom:12}),o.current.on("load",()=>{x(!0)}),o.current.addControl(new(l()).NavigationControl,"top-right"),()=>{o.current&&(o.current.remove(),o.current=null)}},[]),(0,r.useEffect)(()=>{if(o.current&&u&&(d.current.forEach(e=>e.remove()),d.current=[],t.forEach(e=>{if(!e.latitude||!e.longitude)return;let t=document.createElement("div");t.className="custom-marker",t.innerHTML='\n        <div class="bg-zillow-blue text-white px-2 py-1 rounded-lg shadow-lg text-sm font-medium cursor-pointer hover:bg-zillow-blue-dark transition-colors">\n          '.concat((0,a.T4)(e.price),"\n        </div>\n      ");let n=new(l()).Popup({offset:25,closeButton:!0,closeOnClick:!1}).setHTML('\n        <div class="p-4 max-w-sm">\n          <h4 class="font-bold text-zillow-blue mb-2">'.concat((0,a.T4)(e.price),'</h4>\n          <p class="font-medium mb-2">').concat(e.address,'</p>\n          <p class="text-sm text-gray-600 mb-3">').concat(e.description,'</p>\n          <div class="grid grid-cols-2 gap-2 text-xs text-gray-600">\n            <div><strong>Size:</strong> ').concat(e.size,"</div>\n            <div><strong>Zoning:</strong> ").concat(e.zoning,"</div>\n            <div><strong>Chain Potential:</strong> ").concat(e.chainLeasePotential,"</div>\n            <div><strong>Proximity:</strong> ").concat(e.proximity,"</div>\n          </div>\n        </div>\n      ")),i=new(l()).Marker(t).setLngLat([e.longitude,e.latitude]).setPopup(n).addTo(o.current);t.addEventListener("click",()=>{s(e.id)}),d.current.push(i)}),t.length>0)){let e=new(l()).LngLatBounds;t.forEach(t=>{t.latitude&&t.longitude&&e.extend([t.longitude,t.latitude])}),o.current.fitBounds(e,{padding:50,maxZoom:15})}},[t,u,s]),(0,r.useEffect)(()=>{if(!o.current||!n||!n.latitude||!n.longitude)return;o.current.flyTo({center:[n.longitude,n.latitude],zoom:16,duration:1e3});let e=d.current.find(e=>{let t=e.getLngLat();return 1e-4>Math.abs(t.lat-n.latitude)&&1e-4>Math.abs(t.lng-n.longitude)});e&&e.togglePopup()},[n]),(0,i.jsxs)("div",{className:"w-full h-full relative",children:[(0,i.jsx)("div",{ref:c,className:"w-full h-full"}),!u&&(0,i.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100",children:(0,i.jsx)("div",{className:"loading-spinner"})})]})}function o(e){let{properties:t,selectedProperty:n,onPropertySelect:r}=e;return 0===t.length?(0,i.jsxs)("div",{className:"p-6 text-center text-gray-500",children:[(0,i.jsx)("p",{children:"No properties found matching your criteria."}),(0,i.jsx)("p",{className:"text-sm mt-2",children:"Try adjusting your search filters."})]}):(0,i.jsx)("div",{className:"p-6 space-y-4",children:t.map(e=>(0,i.jsxs)("div",{onClick:()=>r(e),className:"property-card ".concat((null==n?void 0:n.id)===e.id?"border-zillow-blue bg-zillow-blue-light":""),children:[(0,i.jsx)("div",{className:"text-xl font-bold text-zillow-blue mb-2",children:(0,a.T4)(e.price)}),(0,i.jsx)("div",{className:"font-medium mb-3",children:e.address}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm text-gray-600 mb-3",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Size:"})," ",e.size]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"$/sq ft:"})," $",e.pricePerSqFt]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Habitability:"})," ",e.habitability]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Days on Market:"})," ",e.daysOnMarket]}),(0,i.jsxs)("div",{className:"col-span-2",children:[(0,i.jsx)("strong",{children:"Proximity:"})," ",e.proximity]})]}),(0,i.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat((0,a.qp)(e.zoning)),children:e.zoning}),(0,i.jsxs)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat((0,a.Wc)(e.chainLeasePotential)),children:["Chain: ",e.chainLeasePotential]})]})]},e.id))})}function d(e){let{properties:t,searchQuery:n,filters:s}=e,[l,a]=(0,r.useState)(null);return(0,i.jsxs)("section",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 h-[600px]",children:[(0,i.jsx)("div",{className:"lg:col-span-2 card p-0 overflow-hidden",children:(0,i.jsx)(c,{properties:t,selectedProperty:l,onPropertyClick:e=>{let n=t.find(t=>t.id===e);n&&a(n)}})}),(0,i.jsxs)("div",{className:"card overflow-hidden flex flex-col",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-zillow-border",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"Search Results"}),(0,i.jsxs)("span",{className:"text-sm text-gray-600",children:[t.length," properties"]})]}),(0,i.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,i.jsx)(o,{properties:t,selectedProperty:l,onPropertySelect:e=>{a(e)}})})]})]})}l().accessToken="pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg"},9314:function(e,t,n){n.d(t,{Providers:function(){return a},l:function(){return l}});var i=n(7437),r=n(2265);let s=(0,r.createContext)(void 0);function l(){let e=(0,r.useContext)(s);if(!e)throw Error("useNotification must be used within a NotificationProvider");return e}function a(e){let{children:t}=e,[n,l]=(0,r.useState)([]),a=(0,r.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",n=Math.random().toString(36).substr(2,9),i={id:n,message:e,type:t};l(e=>[...e,i]),setTimeout(()=>{l(e=>e.filter(e=>e.id!==n))},5e3)},[]),c=(0,r.useCallback)(e=>{l(t=>t.filter(t=>t.id!==e))},[]);return(0,i.jsxs)(s.Provider,{value:{showNotification:a},children:[t,(0,i.jsx)("div",{className:"fixed top-5 right-5 z-50 space-y-2",children:n.map(e=>(0,i.jsx)("div",{className:"notification notification-".concat(e.type," animate-slide-in"),onClick:()=>c(e.id),children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:e.message}),(0,i.jsx)("button",{onClick:()=>c(e.id),className:"ml-4 text-white hover:text-gray-200",children:"\xd7"})]})},e.id))})]})}},2448:function(e,t,n){n.d(t,{y:function(){return a}});var i=n(7437),r=n(2265);let s=(0,n(9763).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var l=n(3247);function a(e){let{onSearch:t,onSyncData:n,isSearching:a}=e,[c,o]=(0,r.useState)(""),[d,u]=(0,r.useState)({}),[x,h]=(0,r.useState)(!1),[m,g]=(0,r.useState)("Never"),p=async()=>{h(!0);try{await n(),g(new Date().toLocaleTimeString())}finally{h(!1)}},f=(e,t)=>{u(n=>({...n,[e]:t||void 0}))};return(0,i.jsxs)("section",{className:"card",children:[(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-zillow-dark-gray mb-4 lg:mb-0",children:"Search Vacant Land in Daytona Beach"}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("button",{onClick:p,disabled:x,className:"btn-success flex items-center space-x-2 ".concat(x?"opacity-50 cursor-not-allowed":""),children:[(0,i.jsx)(s,{size:16,className:x?"animate-spin":""}),(0,i.jsx)("span",{children:x?"Syncing...":"Sync Live Data"})]}),(0,i.jsxs)("span",{className:"text-xs text-gray-500",children:["Last sync: ",m]})]})]}),(0,i.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(c,d)},className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsx)("input",{type:"text",value:c,onChange:e=>o(e.target.value),placeholder:"Search by address, area, or keywords...",className:"input-field"})}),(0,i.jsxs)("button",{type:"submit",disabled:a,className:"btn-primary flex items-center space-x-2 ".concat(a?"opacity-50 cursor-not-allowed":""),children:[(0,i.jsx)(l.Z,{size:16}),(0,i.jsx)("span",{children:a?"Searching...":"Search"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Zoning Type"}),(0,i.jsxs)("select",{value:d.zoning||"",onChange:e=>f("zoning",e.target.value),className:"select-field",children:[(0,i.jsx)("option",{value:"",children:"All Zoning"}),(0,i.jsx)("option",{value:"Commercial",children:"Commercial"}),(0,i.jsx)("option",{value:"Residential",children:"Residential"}),(0,i.jsx)("option",{value:"Industrial",children:"Industrial"}),(0,i.jsx)("option",{value:"Mixed Use",children:"Mixed Use"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Min Price"}),(0,i.jsxs)("select",{value:d.minPrice||"",onChange:e=>f("minPrice",parseInt(e.target.value)),className:"select-field",children:[(0,i.jsx)("option",{value:"",children:"No Min"}),(0,i.jsx)("option",{value:"50000",children:"$50,000"}),(0,i.jsx)("option",{value:"100000",children:"$100,000"}),(0,i.jsx)("option",{value:"200000",children:"$200,000"}),(0,i.jsx)("option",{value:"500000",children:"$500,000"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Max Price"}),(0,i.jsxs)("select",{value:d.maxPrice||"",onChange:e=>f("maxPrice",parseInt(e.target.value)),className:"select-field",children:[(0,i.jsx)("option",{value:"",children:"No Max"}),(0,i.jsx)("option",{value:"100000",children:"$100,000"}),(0,i.jsx)("option",{value:"200000",children:"$200,000"}),(0,i.jsx)("option",{value:"500000",children:"$500,000"}),(0,i.jsx)("option",{value:"1000000",children:"$1,000,000"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-zillow-dark-gray mb-2",children:"Chain Potential"}),(0,i.jsxs)("select",{value:d.chainPotential||"",onChange:e=>f("chainPotential",e.target.value),className:"select-field",children:[(0,i.jsx)("option",{value:"",children:"All Potential"}),(0,i.jsx)("option",{value:"Very High",children:"Very High"}),(0,i.jsx)("option",{value:"High",children:"High"}),(0,i.jsx)("option",{value:"Medium",children:"Medium"}),(0,i.jsx)("option",{value:"Low",children:"Low"})]})]})]})]})]})}},1837:function(e,t,n){n.d(t,{G:function(){return s}});class i extends Error{constructor(e,t){super(e),this.status=t,this.name="APIError"}}async function r(e,t){let n="".concat("https://gold-braid-458901-v2.uc.r.appspot.com").concat(e);try{let e=await fetch(n,{headers:{"Content-Type":"application/json",...null==t?void 0:t.headers},...t});if(!e.ok)throw new i("API request failed: ".concat(e.statusText),e.status);return await e.json()}catch(e){if(e instanceof i)throw e;throw new i("Network error: ".concat(e instanceof Error?e.message:"Unknown error"),0)}}let s={searchProperties:async e=>r("/api/search",{method:"POST",body:JSON.stringify(e)}),getDashboardStats:async()=>r("/api/dashboard/stats"),syncProperties:async()=>r("/api/sync-properties",{method:"POST"}),refreshData:async()=>r("/api/refresh-data",{method:"POST"}),getPropertyDetails:async e=>r("/property?id=".concat(e)),getPropertyByAddress:async e=>r("/property?address=".concat(encodeURIComponent(e))),getStatus:async()=>r("/status")}},3448:function(e,t,n){function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}function r(e){return new Intl.NumberFormat("en-US").format(e)}function s(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}).format(t)}function l(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{hour:"numeric",minute:"2-digit",second:"2-digit"}).format(t)}function a(e){let t=e.toLowerCase();return t.includes("commercial")?"bg-zillow-blue-light text-zillow-blue":t.includes("residential")?"bg-yellow-100 text-yellow-700":t.includes("industrial")?"bg-purple-100 text-purple-700":t.includes("mixed")?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700"}function c(e){let t=e.toLowerCase();return t.includes("very high")?"bg-green-100 text-green-800":t.includes("high")?"bg-green-50 text-green-700":t.includes("medium")?"bg-yellow-100 text-yellow-700":t.includes("low")?"bg-red-100 text-red-700":"bg-gray-100 text-gray-700"}n.d(t,{T4:function(){return i},Wc:function(){return c},ZK:function(){return o},mr:function(){return l},p6:function(){return s},qp:function(){return a},uf:function(){return r}});let o={lat:29.2108,lng:-81.0228}}}]);