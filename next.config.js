/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'gold-braid-458901-v2.uc.r.appspot.com',
      'api.mapbox.com',
      'developer.realestateapi.com'
    ],
  },
  env: {
    NEXT_PUBLIC_API_BASE_URL: 'https://gold-braid-458901-v2.uc.r.appspot.com',
    NEXT_PUBLIC_MAPBOX_TOKEN: 'pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg',
    NEXT_PUBLIC_REAL_ESTATE_API_KEY: 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914',
    NEXT_PUBLIC_REAL_ESTATE_API_URL: 'https://developer.realestateapi.com/reference/property-parcel-api'
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'https://gold-braid-458901-v2.uc.r.appspot.com/api/:path*',
      },
    ];
  },
}

module.exports = nextConfig
